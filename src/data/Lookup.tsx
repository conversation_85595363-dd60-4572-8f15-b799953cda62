

export default {
  SUGGESTIONS: ['Create ToDo App in React', 'Create Budget Track App', 'Create Gym Management Portal Dashboard', 'Create Quiz App On History', 'Create Login Signup Screen'],

  INCREMENTAL_SUGGESTIONS: [
    'Add a search feature to the app',
    'Create a new settings page',
    'Add a delete button to each item',
    'Implement user authentication',
    'Add a dark mode toggle',
    'Create a modal for editing items',
    'Add form validation',
    'Implement sorting functionality'
  ],
  HERO_HEADING: 'What do you want to build?',
  HERO_DESC: 'Prompt, run, edit, and deploy full-stack web apps.',
  INPUT_PLACEHOLDER: 'What you want to build?',
  SIGNIN_HEADING: 'Continue With Hateable',
  SIGNIN_SUBHEADING: 'To use Hateable you must log into an existing account or create one.',
  SIGNIN_AGREEMENT_TEXT: 'By using Hateable, you agree to the collection of usage data for analytics.',

  DEFAULT_FILE: {
    '/public/index.html': {
      code: `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>`
    },
    '/tsconfig.json': {
      code: `{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["**/*.ts", "**/*.tsx"],
  "exclude": ["node_modules"]
}`
    },
    '/App.css': {
      code: `
            @tailwind base;
@tailwind components;
@tailwind utilities;`
    },
    '/tailwind.config.js': {
      code: `
            /** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}", 
    "./public/index.html", 
    "./components/**/*.{js,jsx,ts,tsx}"
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`
    },
    '/postcss.config.js': {
      code: `/** @type {import('postcss-load-config').Config} */
const config = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {}
  },
};

export default config;
`
    }
  },
  DEPENDENCY: {
    "tailwindcss": "^3.4.1",
    "postcss": "^8.4.31",
    "autoprefixer": "^10.4.12",
    "@codesandbox/sandpack-react": "^2.20.0",
    "@google/generative-ai": "^0.22.0",
    "@radix-ui/react-dialog": "^1.1.6",
    "@radix-ui/react-slot": "^1.1.2",
    "@react-oauth/google": "^0.12.1",
    "@types/uuid4": "^2.0.3",
    "axios": "^1.7.9",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "convex": "^1.19.2",
    "dedent": "^1.5.3",
    "lucide-react": "^0.475.0",
    "next": "15.1.7",
    "next-themes": "^0.4.4",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "react-markdown": "^10.0.0",
    "tailwind-merge": "^3.0.2",
    "tailwindcss-animate": "^1.0.7",
    "uuid4": "^2.0.3",
    "react-chartjs-2": "^5.0.1",
    // shadcn/ui dependencies
    "@radix-ui/react-accordion": "^1.1.2",
    "@radix-ui/react-alert-dialog": "^1.0.5",
    "@radix-ui/react-aspect-ratio": "^1.0.3",
    "@radix-ui/react-avatar": "^1.0.4",
    "@radix-ui/react-checkbox": "^1.0.4",
    "@radix-ui/react-collapsible": "^1.0.3",
    "@radix-ui/react-context-menu": "^2.1.5",
    "@radix-ui/react-dropdown-menu": "^2.0.6",
    "@radix-ui/react-hover-card": "^1.0.7",
    "@radix-ui/react-label": "^2.0.2",
    "@radix-ui/react-menubar": "^1.0.4",
    "@radix-ui/react-navigation-menu": "^1.1.4",
    "@radix-ui/react-popover": "^1.0.7",
    "@radix-ui/react-progress": "^1.0.3",
    "@radix-ui/react-radio-group": "^1.1.3",
    "@radix-ui/react-scroll-area": "^1.0.5",
    "@radix-ui/react-select": "^2.0.0",
    "@radix-ui/react-separator": "^1.0.3",
    "@radix-ui/react-slider": "^1.1.2",
    "@radix-ui/react-switch": "^1.0.3",
    "@radix-ui/react-tabs": "^1.0.4",
    "@radix-ui/react-toggle": "^1.0.3",
    "@radix-ui/react-toggle-group": "^1.0.4",
    "@radix-ui/react-tooltip": "^1.0.7",
    "@hookform/resolvers": "^3.3.2",
    "react-hook-form": "^7.48.2",
    "react-day-picker": "^8.9.1",
    "date-fns": "^2.30.0",
    "cmdk": "^0.2.0",
    "embla-carousel-react": "^8.0.0",
    "recharts": "^2.8.0",
    "input-otp": "^1.2.4",
    "react-resizable-panels": "^0.0.55",
    "sonner": "^1.4.0",
    "vaul": "^0.9.0",
    "zod": "^3.22.4",
  },

  // Available shadcn components mapping - COMPREHENSIVE LIST
  SHADCN_COMPONENTS: {
    "Accordion": {
      import: "import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'",
      usage: "<Accordion type='single' collapsible><AccordionItem value='item-1'><AccordionTrigger>Title</AccordionTrigger><AccordionContent>Content</AccordionContent></AccordionItem></Accordion>",
      subComponents: ["AccordionContent", "AccordionItem", "AccordionTrigger"]
    },
    "AlertDialog": {
      import: "import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'",
      usage: "<AlertDialog><AlertDialogTrigger>Open</AlertDialogTrigger><AlertDialogContent><AlertDialogHeader><AlertDialogTitle>Title</AlertDialogTitle><AlertDialogDescription>Description</AlertDialogDescription></AlertDialogHeader><AlertDialogFooter><AlertDialogCancel>Cancel</AlertDialogCancel><AlertDialogAction>Continue</AlertDialogAction></AlertDialogFooter></AlertDialogContent></AlertDialog>",
      subComponents: ["AlertDialogAction", "AlertDialogCancel", "AlertDialogContent", "AlertDialogDescription", "AlertDialogFooter", "AlertDialogHeader", "AlertDialogTitle", "AlertDialogTrigger"]
    },
    "Alert": {
      import: "import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'",
      usage: "<Alert><AlertTitle>Alert Title</AlertTitle><AlertDescription>Alert description</AlertDescription></Alert>",
      subComponents: ["AlertDescription", "AlertTitle"]
    },
    "AspectRatio": {
      import: "import { AspectRatio } from '@/components/ui/aspect-ratio'",
      usage: "<AspectRatio ratio={16 / 9}><img src='...' alt='...' /></AspectRatio>"
    },
    "Avatar": {
      import: "import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'",
      usage: "<Avatar><AvatarImage src='...' /><AvatarFallback>CN</AvatarFallback></Avatar>",
      subComponents: ["AvatarFallback", "AvatarImage"]
    },
    "Badge": {
      import: "import { Badge } from '@/components/ui/badge'",
      usage: "<Badge variant='default'>Badge</Badge>",
      variants: ["default", "secondary", "destructive", "outline"]
    },
    "Breadcrumb": {
      import: "import { Breadcrumb, BreadcrumbEllipsis, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'",
      usage: "<Breadcrumb><BreadcrumbList><BreadcrumbItem><BreadcrumbLink href='/'>Home</BreadcrumbLink></BreadcrumbItem><BreadcrumbSeparator /><BreadcrumbItem><BreadcrumbPage>Current</BreadcrumbPage></BreadcrumbItem></BreadcrumbList></Breadcrumb>",
      subComponents: ["BreadcrumbEllipsis", "BreadcrumbItem", "BreadcrumbLink", "BreadcrumbList", "BreadcrumbPage", "BreadcrumbSeparator"]
    },
    "Button": {
      import: "import { Button } from '@/components/ui/button'",
      usage: "<Button variant='default' size='default'>Click me</Button>",
      variants: ["default", "destructive", "outline", "secondary", "ghost", "link"],
      sizes: ["default", "sm", "lg", "icon"]
    },
    "Calendar": {
      import: "import { Calendar } from '@/components/ui/calendar'",
      usage: "<Calendar mode='single' selected={date} onSelect={setDate} />"
    },
    "Card": {
      import: "import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card'",
      usage: "<Card><CardHeader><CardTitle>Title</CardTitle><CardDescription>Description</CardDescription></CardHeader><CardContent>Content</CardContent></Card>",
      subComponents: ["CardHeader", "CardTitle", "CardDescription", "CardContent", "CardFooter", "CardAction"]
    },
    "Carousel": {
      import: "import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel'",
      usage: "<Carousel><CarouselContent><CarouselItem>Item 1</CarouselItem><CarouselItem>Item 2</CarouselItem></CarouselContent><CarouselPrevious /><CarouselNext /></Carousel>",
      subComponents: ["CarouselContent", "CarouselItem", "CarouselNext", "CarouselPrevious"]
    },
    "Chart": {
      import: "import { Chart, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart'",
      usage: "<ChartContainer><Chart data={data}><ChartTooltip content={<ChartTooltipContent />} /></Chart></ChartContainer>",
      subComponents: ["ChartContainer", "ChartTooltip", "ChartTooltipContent"]
    },
    "Checkbox": {
      import: "import { Checkbox } from '@/components/ui/checkbox'",
      usage: "<Checkbox id='terms' />"
    },
    "Collapsible": {
      import: "import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'",
      usage: "<Collapsible><CollapsibleTrigger>Toggle</CollapsibleTrigger><CollapsibleContent>Content</CollapsibleContent></Collapsible>",
      subComponents: ["CollapsibleContent", "CollapsibleTrigger"]
    },
    "Command": {
      import: "import { Command, CommandDialog, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator, CommandShortcut } from '@/components/ui/command'",
      usage: "<Command><CommandInput placeholder='Type a command...' /><CommandList><CommandEmpty>No results found.</CommandEmpty><CommandGroup heading='Suggestions'><CommandItem>Item</CommandItem></CommandGroup></CommandList></Command>",
      subComponents: ["CommandDialog", "CommandEmpty", "CommandGroup", "CommandInput", "CommandItem", "CommandList", "CommandSeparator", "CommandShortcut"]
    },
    "ContextMenu": {
      import: "import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from '@/components/ui/context-menu'",
      usage: "<ContextMenu><ContextMenuTrigger>Right click</ContextMenuTrigger><ContextMenuContent><ContextMenuItem>Item</ContextMenuItem></ContextMenuContent></ContextMenu>",
      subComponents: ["ContextMenuContent", "ContextMenuItem", "ContextMenuTrigger"]
    },
    "Dialog": {
      import: "import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'",
      usage: "<Dialog><DialogTrigger>Open</DialogTrigger><DialogContent><DialogHeader><DialogTitle>Title</DialogTitle><DialogDescription>Description</DialogDescription></DialogHeader><DialogFooter>Footer</DialogFooter></DialogContent></Dialog>",
      subComponents: ["DialogContent", "DialogDescription", "DialogFooter", "DialogHeader", "DialogTitle", "DialogTrigger"]
    },
    "Drawer": {
      import: "import { Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer'",
      usage: "<Drawer><DrawerTrigger>Open</DrawerTrigger><DrawerContent><DrawerHeader><DrawerTitle>Title</DrawerTitle><DrawerDescription>Description</DrawerDescription></DrawerHeader><DrawerFooter><DrawerClose>Close</DrawerClose></DrawerFooter></DrawerContent></Drawer>",
      subComponents: ["DrawerClose", "DrawerContent", "DrawerDescription", "DrawerFooter", "DrawerHeader", "DrawerTitle", "DrawerTrigger"]
    },
    "DropdownMenu": {
      import: "import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'",
      usage: "<DropdownMenu><DropdownMenuTrigger>Open</DropdownMenuTrigger><DropdownMenuContent><DropdownMenuLabel>My Account</DropdownMenuLabel><DropdownMenuSeparator /><DropdownMenuItem>Profile</DropdownMenuItem></DropdownMenuContent></DropdownMenu>",
      subComponents: ["DropdownMenuContent", "DropdownMenuItem", "DropdownMenuLabel", "DropdownMenuSeparator", "DropdownMenuTrigger"]
    },
    "Form": {
      import: "import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'",
      usage: "<Form><FormField><FormItem><FormLabel>Label</FormLabel><FormControl><Input /></FormControl><FormDescription>Description</FormDescription><FormMessage /></FormItem></FormField></Form>",
      subComponents: ["FormControl", "FormDescription", "FormField", "FormItem", "FormLabel", "FormMessage"]
    },
    "HoverCard": {
      import: "import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card'",
      usage: "<HoverCard><HoverCardTrigger>Hover</HoverCardTrigger><HoverCardContent>Content</HoverCardContent></HoverCard>",
      subComponents: ["HoverCardContent", "HoverCardTrigger"]
    },
    "Input": {
      import: "import { Input } from '@/components/ui/input'",
      usage: "<Input type='text' placeholder='Enter text...' />",
      types: ["text", "email", "password", "number", "tel", "url", "search"]
    },
    "InputOtp": {
      import: "import { InputOTP, InputOTPGroup, InputOTPSeparator, InputOTPSlot } from '@/components/ui/input-otp'",
      usage: "<InputOTP maxLength={6}><InputOTPGroup><InputOTPSlot index={0} /><InputOTPSlot index={1} /></InputOTPGroup><InputOTPSeparator /><InputOTPGroup><InputOTPSlot index={2} /></InputOTPGroup></InputOTP>",
      subComponents: ["InputOTPGroup", "InputOTPSeparator", "InputOTPSlot"]
    },
    "Label": {
      import: "import { Label } from '@/components/ui/label'",
      usage: "<Label htmlFor='input'>Label text</Label>"
    },
    "Textarea": {
      import: "import { Textarea } from '@/components/ui/textarea'",
      usage: "<Textarea placeholder='Enter your message...' />"
    },
    "Select": {
      import: "import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'",
      usage: "<Select><SelectTrigger><SelectValue placeholder='Select option' /></SelectTrigger><SelectContent><SelectItem value='option1'>Option 1</SelectItem></SelectContent></Select>",
      subComponents: ["SelectContent", "SelectItem", "SelectTrigger", "SelectValue"]
    },
    "RadioGroup": {
      import: "import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'",
      usage: "<RadioGroup><div className='flex items-center space-x-2'><RadioGroupItem value='option1' id='option1' /><Label htmlFor='option1'>Option 1</Label></div></RadioGroup>",
      subComponents: ["RadioGroupItem"]
    },
    "Switch": {
      import: "import { Switch } from '@/components/ui/switch'",
      usage: "<Switch />"
    },
    "Tabs": {
      import: "import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'",
      usage: "<Tabs defaultValue='tab1'><TabsList><TabsTrigger value='tab1'>Tab 1</TabsTrigger></TabsList><TabsContent value='tab1'>Content</TabsContent></Tabs>",
      subComponents: ["TabsContent", "TabsList", "TabsTrigger"]
    },
    "Sheet": {
      import: "import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'",
      usage: "<Sheet><SheetTrigger asChild><Button>Open</Button></SheetTrigger><SheetContent><SheetHeader><SheetTitle>Title</SheetTitle><SheetDescription>Description</SheetDescription></SheetHeader></SheetContent></Sheet>",
      subComponents: ["SheetContent", "SheetDescription", "SheetHeader", "SheetTitle", "SheetTrigger", "SheetFooter"]
    },
    "Popover": {
      import: "import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'",
      usage: "<Popover><PopoverTrigger asChild><Button>Open</Button></PopoverTrigger><PopoverContent>Content</PopoverContent></Popover>",
      subComponents: ["PopoverContent", "PopoverTrigger"]
    },
    "Tooltip": {
      import: "import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'",
      usage: "<TooltipProvider><Tooltip><TooltipTrigger asChild><Button>Hover me</Button></TooltipTrigger><TooltipContent>Tooltip content</TooltipContent></Tooltip></TooltipProvider>",
      subComponents: ["TooltipContent", "TooltipProvider", "TooltipTrigger"]
    },
    "Progress": {
      import: "import { Progress } from '@/components/ui/progress'",
      usage: "<Progress value={33} />"
    },
    "Skeleton": {
      import: "import { Skeleton } from '@/components/ui/skeleton'",
      usage: "<Skeleton className='w-[100px] h-[20px] rounded-full' />"
    },
    "Table": {
      import: "import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'",
      usage: "<Table><TableCaption>Caption</TableCaption><TableHeader><TableRow><TableHead>Header</TableHead></TableRow></TableHeader><TableBody><TableRow><TableCell>Cell</TableCell></TableRow></TableBody></Table>",
      subComponents: ["TableBody", "TableCaption", "TableCell", "TableHead", "TableHeader", "TableRow"]
    },
    "Loading": {
      import: "import { LoadingSpinner, LoadingState, FullPageLoading, ButtonLoading } from '@/components/ui/loading'",
      usage: "<LoadingSpinner size='default' />",
      subComponents: ["LoadingSpinner", "LoadingState", "FullPageLoading", "ButtonLoading"]
    },
    "Menubar": {
      import: "import { Menubar, MenubarContent, MenubarItem, MenubarMenu, MenubarSeparator, MenubarShortcut, MenubarTrigger } from '@/components/ui/menubar'",
      usage: "<Menubar><MenubarMenu><MenubarTrigger>File</MenubarTrigger><MenubarContent><MenubarItem>New</MenubarItem></MenubarContent></MenubarMenu></Menubar>",
      subComponents: ["MenubarContent", "MenubarItem", "MenubarMenu", "MenubarSeparator", "MenubarShortcut", "MenubarTrigger"]
    },
    "NavigationMenu": {
      import: "import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger } from '@/components/ui/navigation-menu'",
      usage: "<NavigationMenu><NavigationMenuList><NavigationMenuItem><NavigationMenuTrigger>Item</NavigationMenuTrigger><NavigationMenuContent>Content</NavigationMenuContent></NavigationMenuItem></NavigationMenuList></NavigationMenu>",
      subComponents: ["NavigationMenuContent", "NavigationMenuItem", "NavigationMenuLink", "NavigationMenuList", "NavigationMenuTrigger"]
    },
    "Pagination": {
      import: "import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination'",
      usage: "<Pagination><PaginationContent><PaginationItem><PaginationPrevious href='#' /></PaginationItem><PaginationItem><PaginationLink href='#'>1</PaginationLink></PaginationItem><PaginationItem><PaginationNext href='#' /></PaginationItem></PaginationContent></Pagination>",
      subComponents: ["PaginationContent", "PaginationEllipsis", "PaginationItem", "PaginationLink", "PaginationNext", "PaginationPrevious"]
    },
    "Resizable": {
      import: "import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable'",
      usage: "<ResizablePanelGroup direction='horizontal'><ResizablePanel>Panel 1</ResizablePanel><ResizableHandle /><ResizablePanel>Panel 2</ResizablePanel></ResizablePanelGroup>",
      subComponents: ["ResizableHandle", "ResizablePanel", "ResizablePanelGroup"]
    },
    "ScrollArea": {
      import: "import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'",
      usage: "<ScrollArea className='h-[200px]'>Content</ScrollArea>",
      subComponents: ["ScrollBar"]
    },
    "Separator": {
      import: "import { Separator } from '@/components/ui/separator'",
      usage: "<Separator />"
    },
    "Sidebar": {
      import: "import { Sidebar, SidebarContent, SidebarFooter, SidebarGroup, SidebarGroupContent, SidebarGroupLabel, SidebarHeader, SidebarInset, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarMenuSub, SidebarMenuSubButton, SidebarMenuSubItem, SidebarProvider, SidebarRail, SidebarSeparator, SidebarTrigger } from '@/components/ui/sidebar'",
      usage: "<SidebarProvider><Sidebar><SidebarHeader /><SidebarContent><SidebarGroup><SidebarGroupLabel>Group</SidebarGroupLabel><SidebarGroupContent><SidebarMenu><SidebarMenuItem><SidebarMenuButton>Item</SidebarMenuButton></SidebarMenuItem></SidebarMenu></SidebarGroupContent></SidebarGroup></SidebarContent><SidebarFooter /></Sidebar></SidebarProvider>",
      subComponents: ["SidebarContent", "SidebarFooter", "SidebarGroup", "SidebarGroupContent", "SidebarGroupLabel", "SidebarHeader", "SidebarInset", "SidebarMenu", "SidebarMenuButton", "SidebarMenuItem", "SidebarMenuSub", "SidebarMenuSubButton", "SidebarMenuSubItem", "SidebarProvider", "SidebarRail", "SidebarSeparator", "SidebarTrigger"]
    },
    "Slider": {
      import: "import { Slider } from '@/components/ui/slider'",
      usage: "<Slider defaultValue={[50]} max={100} step={1} />"
    },
    "Sonner": {
      import: "import { toast } from 'sonner'",
      usage: "toast('Hello World')"
    },
    "Toggle": {
      import: "import { Toggle } from '@/components/ui/toggle'",
      usage: "<Toggle>Toggle</Toggle>"
    },
    "ToggleGroup": {
      import: "import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'",
      usage: "<ToggleGroup type='single'><ToggleGroupItem value='a'>A</ToggleGroupItem><ToggleGroupItem value='b'>B</ToggleGroupItem></ToggleGroup>",
      subComponents: ["ToggleGroupItem"]
    }
  },
  PRICING_DESC: 'Start with a free account to speed up your workflow on public projects or boost your entire team with instantly-opening production environments.',
  PRICING_OPTIONS: [
    {
      name: 'Basic',
      tokens: '50K',
      value: 50000,
      desc: 'Ideal for hobbyists and casual users for light, exploratory use.',
      price: 4.99
    },
    {
      name: 'Starter',
      tokens: '120K',
      value: 120000,
      desc: 'Designed for professionals who need to use Bolt a few times per week.',
      price: 9.99
    },
    {
      name: 'Pro',
      tokens: '2.5M',
      value: 2500000,
      desc: 'Designed for professionals who need to use Bolt a few times per week.',
      price: 19.99
    },
    {
      name: 'Unlimted (License)',
      tokens: 'Unmited',
      value: *********,
      desc: 'Designed for professionals who need to use Bolt a few times per week.',
      price: 49.99
    }
  ]


}