import { CodeSandbox } from '@codesandbox/sdk';

export interface SandboxFile {
  code: string;
}

export interface SandboxFiles {
  [path: string]: SandboxFile;
}

export interface SandboxConfig {
  files: SandboxFiles;
  dependencies?: Record<string, string>;
  template?: string;
}

export interface SandboxInfo {
  id: string;
  url: string;
  previewUrl: string;
  editUrl: string;
}

export class CodeSandboxSDKService {
  private sdk: CodeSandbox | null = null;
  private currentSandbox: any = null;
  private apiToken: string | null = null;
  private currentSandboxId: string | null = null;

  constructor(apiToken?: string) {
    this.apiToken = apiToken || process.env.NEXT_PUBLIC_CODESANDBOX_API_TOKEN || null;
    if (this.apiToken) {
      this.sdk = new CodeSandbox(this.apiToken);
    }
  }

  /**
   * Initialize the SDK with an API token
   */
  initialize(apiToken: string) {
    this.apiToken = apiToken;
    this.sdk = new CodeSandbox(apiToken);
  }

  /**
   * Check if the SDK is properly initialized
   */
  isInitialized(): boolean {
    return this.sdk !== null;
  }

  /**
   * Create sandbox using legacy API (no authentication required)
   */
  private async createSandboxLegacy(config: SandboxConfig): Promise<SandboxInfo> {
    const filesForApi: { [key: string]: { content: string } } = {};

    Object.entries(config.files).forEach(([filename, filedata]) => {
      filesForApi[filename.replace(/^\//, '')] = { content: filedata.code || '' };
    });

    // Add package.json with dependencies if not present
    if (!filesForApi['package.json'] && config.dependencies) {
      const packageJson = {
        name: 'react-typescript-app',
        version: '0.1.0',
        private: true,
        dependencies: {
          'react': '^18.2.0',
          'react-dom': '^18.2.0',
          'typescript': '^4.9.5',
          ...config.dependencies
        },
        scripts: {
          start: 'react-scripts start',
          build: 'react-scripts build',
          test: 'react-scripts test',
          eject: 'react-scripts eject'
        },
        eslintConfig: {
          extends: ['react-app', 'react-app/jest']
        },
        browserslist: {
          production: ['>0.2%', 'not dead', 'not op_mini all'],
          development: ['last 1 chrome version', 'last 1 firefox version', 'last 1 safari version']
        }
      };
      filesForApi['package.json'] = { content: JSON.stringify(packageJson, null, 2) };
    }

    const parameters = {
      files: filesForApi,
    };

    const apiUrl = 'https://codesandbox.io/api/v1/sandboxes/define?json=1';

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(parameters),
      });

      const data = await response.json();

      if (data.sandbox_id) {
        this.currentSandboxId = data.sandbox_id;

        return {
          id: data.sandbox_id,
          url: `https://codesandbox.io/p/sandbox/${data.sandbox_id}`,
          previewUrl: `https://${data.sandbox_id}.csb.app/`,
          editUrl: `https://codesandbox.io/p/sandbox/${data.sandbox_id}`
        };
      } else {
        throw new Error('Failed to create sandbox: No sandbox ID returned');
      }
    } catch (error) {
      console.error('Error creating sandbox with legacy API:', error);
      throw new Error(`Failed to create sandbox: ${error}`);
    }
  }

  /**
   * Create a new sandbox with react-ts template
   */
  async createSandbox(config: SandboxConfig): Promise<SandboxInfo> {
    // Try SDK first if available, otherwise use legacy API
    if (this.sdk) {
      try {
        // Create sandbox from default template (universal template supports React/TypeScript)
        const sandbox = await this.sdk.sandboxes.create({
          title: 'React TypeScript App',
          description: 'Generated by SparkForge AI',
          privacy: 'public'
        });

        this.currentSandbox = sandbox;

        // Write files to the sandbox
        await this.updateFiles(config.files);

        // Generate URLs
        const sandboxInfo: SandboxInfo = {
          id: sandbox.id,
          url: `https://codesandbox.io/p/sandbox/${sandbox.id}`,
          previewUrl: `https://${sandbox.id}.csb.app/`,
          editUrl: `https://codesandbox.io/p/sandbox/${sandbox.id}`
        };

        return sandboxInfo;
      } catch (error) {
        console.error('Error creating sandbox with SDK, falling back to legacy API:', error);
        // Fall back to legacy API
        return this.createSandboxLegacy(config);
      }
    } else {
      // Use legacy API when SDK is not available
      return this.createSandboxLegacy(config);
    }
  }

  /**
   * Update files in the current sandbox
   */
  async updateFiles(files: SandboxFiles): Promise<void> {
    if (this.currentSandbox) {
      // SDK method
      try {
        // Update files in the sandbox
        for (const [path, file] of Object.entries(files)) {
          const cleanPath = path.startsWith('/') ? path.slice(1) : path;
          await this.currentSandbox.fs.writeTextFile(cleanPath, file.code);
        }
      } catch (error) {
        console.error('Error updating files with SDK:', error);
        throw new Error(`Failed to update files: ${error}`);
      }
    } else if (this.currentSandboxId) {
      // Legacy method - create a new sandbox with updated files
      console.log('Legacy API does not support file updates. Consider creating a new sandbox.');
      // For legacy API, we can't update files directly, so we just log this
      // In a real implementation, you might want to create a new sandbox
    } else {
      throw new Error('No active sandbox. Create a sandbox first.');
    }
  }

  /**
   * Get the current sandbox information
   */
  getCurrentSandboxInfo(): SandboxInfo | null {
    if (!this.currentSandbox) {
      return null;
    }

    return {
      id: this.currentSandbox.id,
      url: `https://codesandbox.io/p/sandbox/${this.currentSandbox.id}`,
      previewUrl: `https://${this.currentSandbox.id}.csb.app/`,
      editUrl: `https://codesandbox.io/p/sandbox/${this.currentSandbox.id}`
    };
  }

  /**
   * Create a shareable URL for the current sandbox
   */
  async createShareableUrl(): Promise<string> {
    if (!this.currentSandbox) {
      throw new Error('No active sandbox. Create a sandbox first.');
    }

    try {
      // The sandbox URL is already shareable
      return `https://codesandbox.io/p/sandbox/${this.currentSandbox.id}`;
    } catch (error) {
      console.error('Error creating shareable URL:', error);
      throw new Error(`Failed to create shareable URL: ${error}`);
    }
  }

  /**
   * Get the preview URL for the current sandbox
   */
  getPreviewUrl(): string | null {
    if (!this.currentSandbox) {
      return null;
    }
    return `https://${this.currentSandbox.id}.csb.app/`;
  }

  /**
   * Fork the current sandbox
   */
  async forkSandbox(): Promise<SandboxInfo> {
    if (!this.currentSandbox) {
      throw new Error('No active sandbox to fork.');
    }

    try {
      const forkedSandbox = await this.currentSandbox.fork();
      
      return {
        id: forkedSandbox.id,
        url: `https://codesandbox.io/p/sandbox/${forkedSandbox.id}`,
        previewUrl: `https://${forkedSandbox.id}.csb.app/`,
        editUrl: `https://codesandbox.io/p/sandbox/${forkedSandbox.id}`
      };
    } catch (error) {
      console.error('Error forking sandbox:', error);
      throw new Error(`Failed to fork sandbox: ${error}`);
    }
  }

  /**
   * Close the current sandbox
   */
  async closeSandbox(): Promise<void> {
    if (this.currentSandbox) {
      try {
        await this.currentSandbox.hibernate();
        this.currentSandbox = null;
      } catch (error) {
        console.error('Error closing sandbox:', error);
        // Don't throw here, just log the error
      }
    }
  }

  /**
   * Convert legacy Sandpack files format to our format
   */
  static convertSandpackFiles(sandpackFiles: any): SandboxFiles {
    const convertedFiles: SandboxFiles = {};
    
    Object.entries(sandpackFiles).forEach(([path, fileData]: [string, any]) => {
      convertedFiles[path] = {
        code: fileData.code || fileData || ''
      };
    });

    return convertedFiles;
  }

  /**
   * Create default React TypeScript files structure
   */
  static createDefaultReactTSFiles(): SandboxFiles {
    return {
      '/src/App.tsx': {
        code: `import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to React with TypeScript</h1>
        <p>Start building your amazing app!</p>
      </header>
    </div>
  );
}

export default App;`
      },
      '/src/index.tsx': {
        code: `import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`
      },
      '/src/App.css': {
        code: `@tailwind base;
@tailwind components;
@tailwind utilities;

.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}`
      },
      '/src/index.css': {
        code: `@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}`
      },
      '/public/index.html': {
        code: `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="React TypeScript App" />
    <title>React TypeScript App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>`
      },
      '/package.json': {
        code: `{
  "name": "react-typescript-app",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^4.9.5"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}`
      }
    };
  }
}

// Export a singleton instance
export const codesandboxService = new CodeSandboxSDKService();
