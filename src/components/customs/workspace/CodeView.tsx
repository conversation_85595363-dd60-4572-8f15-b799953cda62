import React, { useContext, useEffect, useState } from 'react'
import Lookup from '@/data/Lookup';
import { MessageContext } from '@/context/MessageContext';
import { Prompt } from '@/data/prompt';
import axios from 'axios';
import { useParams } from 'next/navigation';
import { useConvex, useMutation } from 'convex/react';
import { api } from '../../../../convex/_generated/api';
import { Id } from '../../../../convex/_generated/dataModel';
import { CloudDownload, ExternalLink, Loader, TrendingUp, Code, Eye, FileText } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { countToken } from './ChatView';
import JSZip from 'jszip';
import { IncrementalEditor, type IncrementalEditResponse } from '@/lib/incremental-editor';
import { codesandboxService, type SandboxFiles, type SandboxInfo } from '@/lib/codesandbox-sdk-service';

const CodeView = () => {
  const [activeTab, setActiveTab] = useState('code');
  const [files, setFiles] = useState(Lookup.DEFAULT_FILE);
  const {messages} = useContext<any>(MessageContext)
  const [loading, setLoading] = useState(false);
  const [editMode, setEditMode] = useState<'full' | 'incremental' | null>(null);
  const {id} = useParams();
  const {data: session} = useSession();
  const UpdateToken = useMutation(api.users.updadteUserToken);

  const convex = useConvex();
  const UpdateFiles=useMutation(api.workspace.updateFileData)
  const [dynamicDeps, setDynamicDeps] = useState({});
  const [sandboxInfo, setSandboxInfo] = useState<SandboxInfo | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [isCreatingSandbox, setIsCreatingSandbox] = useState(false);

  useEffect(()=>{
    if(messages?.length > 0){
       const role= messages[messages.length-1].role;
        if(role === 'user'){
          GenerateAiCode()
        }
    }
  },[messages])

  const GetFiles=async()=>{
    setLoading(true)
    const result= await convex.query(api.workspace.getWorkSpace,{
      workspaceId:id as any
    })
    const mergeFiles= {...result?.filedata}
    setFiles(mergeFiles)
    setLoading(false)
  }

  useEffect(() => {
    if (!id) return;
    GetFiles();
  }, [id]);

  // Create or update sandbox when files change
  useEffect(() => {
    if (Object.keys(files).length > 0 && !isCreatingSandbox) {
      createOrUpdateSandbox();
    }
  }, [files]);

  const createOrUpdateSandbox = async () => {
    try {
      setIsCreatingSandbox(true);

      // Convert files to SDK format
      const sandboxFiles: SandboxFiles = {};
      Object.entries(files).forEach(([path, fileData]: [string, any]) => {
        sandboxFiles[path] = {
          code: fileData.code || fileData || ''
        };
      });

      // Create sandbox config
      const config = {
        files: sandboxFiles,
        dependencies: {
          ...Lookup.DEPENDENCY,
          ...dynamicDeps
        },
        template: 'react-ts'
      };

      if (!sandboxInfo) {
        // Create new sandbox
        const newSandboxInfo = await codesandboxService.createSandbox(config);
        setSandboxInfo(newSandboxInfo);
        setPreviewUrl(newSandboxInfo.previewUrl);
      } else {
        // Update existing sandbox
        await codesandboxService.updateFiles(sandboxFiles);
      }
    } catch (error) {
      console.error('Error creating/updating sandbox:', error);
      // Fallback to creating a new sandbox if update fails
      if (sandboxInfo) {
        try {
          const sandboxFiles: SandboxFiles = {};
          Object.entries(files).forEach(([path, fileData]: [string, any]) => {
            sandboxFiles[path] = {
              code: fileData.code || fileData || ''
            };
          });

          const config = {
            files: sandboxFiles,
            dependencies: {
              ...Lookup.DEPENDENCY,
              ...dynamicDeps
            },
            template: 'react-ts'
          };

          const newSandboxInfo = await codesandboxService.createSandbox(config);
          setSandboxInfo(newSandboxInfo);
          setPreviewUrl(newSandboxInfo.previewUrl);
        } catch (fallbackError) {
          console.error('Fallback sandbox creation failed:', fallbackError);
        }
      }
    } finally {
      setIsCreatingSandbox(false);
    }
  };

const GenerateAiCode = async () => {
  setActiveTab('code');
  setLoading(true);

  try {
    // Get the latest user message
    const latestMessage = messages[messages.length - 1];
    const userPrompt = latestMessage?.content || '';

    // Determine if we should use incremental editing or full regeneration
    const shouldUseIncremental = IncrementalEditor.shouldUseIncrementalEdit(userPrompt, files);

    // Update edit mode for UI feedback
    setEditMode(shouldUseIncremental ? 'incremental' : 'full');

    console.log(`Using ${shouldUseIncremental ? 'incremental' : 'full'} code generation for prompt: "${userPrompt}"`);

    let aiResp: any = {};
    let newFiles: any = {};

    if (shouldUseIncremental) {
      // Use incremental editing
      const editType = IncrementalEditor.determineEditType(userPrompt);
      const result = await axios.post('/api/edit-code', {
        prompt: userPrompt,
        existingFiles: files,
        editType
      });

      const editResponse: IncrementalEditResponse = result?.data || {};

      // Merge the incremental changes with existing files
      newFiles = IncrementalEditor.mergeEditResults(files, editResponse);

      // Generate change summary for user feedback
      const changeSummary = IncrementalEditor.generateChangeSummary(editResponse);
      console.log("Incremental edit summary:", changeSummary);

      // Use the edit response as aiResp for consistency
      aiResp = {
        files: newFiles,
        explanation: editResponse.summary,
        detectedShadcnComponents: editResponse.detectedShadcnComponents,
        requiredShadcnImports: editResponse.requiredShadcnImports,
        editType: 'incremental',
        changeSummary
      };
    } else {
      // Use full regeneration
      const prompt = JSON.stringify(messages) + " " + Prompt.CODE_GEN_PROMPT;
      const result = await axios.post('/api/gen-ai-code', { prompt });

      aiResp = result?.data || {};
      newFiles = { ...files, ...(aiResp?.files || {}) };
    }

    setFiles(newFiles);

    // Use enhanced dependencies that include shadcn components
    const enhancedDeps = {
      ...Lookup.DEPENDENCY,
      ...(aiResp?.dependencies || {}),
      ...(aiResp?.requiredDependencies || {})
    };
    setDynamicDeps(enhancedDeps);

    await UpdateFiles({
      workspaceId: id as Id<'workSpaces'>,
      filedata: newFiles,
    });

    // Log detected shadcn components for debugging
    if (aiResp?.detectedShadcnComponents?.length > 0) {
      console.log("Detected shadcn components:", aiResp.detectedShadcnComponents);
      console.log("Required imports:", aiResp.requiredShadcnImports);
    }

    const aiTokenUsage = countToken(JSON.stringify(newFiles));
    const updatedToken = Math.max(0, Number(session?.user?.token || 0) - aiTokenUsage);

    await UpdateToken({
      userId: session?.user?._id as Id<'users'>,
      token: updatedToken,
    });

  } catch (error) {
    console.error("Code generation or Convex error:", error);
  } finally {
    setLoading(false);
    setEditMode(null);
  }
};


  const handleExport = async () => {
    const zip = new JSZip();
    Object.entries(files).forEach(([filename, filedata]) => {
      zip.file(filename.replace(/^\//, ''), filedata.code || '');
    });
    const blob = await zip.generateAsync({ type: 'blob' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'project.zip';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const [deploying, setDeploying] = useState(false);

  const handleDeploy = async (isShareButtonClicked: boolean) => {
    if (!sandboxInfo) {
      alert('No sandbox available. Please wait for the sandbox to be created.');
      return;
    }

    setDeploying(true);

    try {
      if (isShareButtonClicked) {
        window.open(sandboxInfo.editUrl, '_blank');
      } else {
        // For deploy, we can open the preview URL
        window.open(sandboxInfo.previewUrl, '_blank');
      }
    } catch (error) {
      console.error('Error opening sandbox:', error);
      alert('Error opening sandbox: ' + (error as any).message);
    } finally {
      setDeploying(false);
    }
  };

  const renderCodeEditor = () => {
    return (
      <div className="h-[80vh] bg-gray-900 text-white p-4 overflow-auto">
        <div className="space-y-4">
          {Object.entries(files).map(([filename, filedata]: [string, any]) => (
            <div key={filename} className="border border-gray-700 rounded-lg">
              <div className="bg-gray-800 px-4 py-2 border-b border-gray-700 flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span className="text-sm font-mono">{filename}</span>
              </div>
              <div className="p-4">
                <pre className="text-sm font-mono whitespace-pre-wrap overflow-x-auto">
                  {filedata.code || filedata || ''}
                </pre>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderPreview = () => {
    if (!previewUrl) {
      return (
        <div className="h-[80vh] bg-gray-900 flex items-center justify-center">
          <div className="text-center text-white">
            <Loader className="animate-spin h-8 w-8 mx-auto mb-4" />
            <p>Creating sandbox preview...</p>
          </div>
        </div>
      );
    }

    return (
      <div className="h-[80vh]">
        <iframe
          src={previewUrl}
          className="w-full h-full border-0"
          title="Preview"
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
        />
      </div>
    );
  };

  return (
    <div className='w-full h-full flex flex-col relative '>
      <div className='flex justify-between items-center rounded-t-sm p-2 bg-[#181818] gap-2 text-white'>
        <div className='bg-black  rounded-full text-sm flex gap-2 '>
          <button
            className={`py-1 px-4 rounded-l-full rounded-r-md flex items-center gap-2 ${activeTab === 'code' ? 'border bg-gray-900 ' : ''}`}
            onClick={() => setActiveTab('code')}
          >
            <Code className="h-4 w-4" />
            Code
          </button>
          <button
            className={`py-1 px-3 rounded-r-full rounded-l-md flex items-center gap-2 ${activeTab === 'preview' ? 'border bg-gray-900 ' : ''}`}
            onClick={() => setActiveTab('preview')}
          >
            <Eye className="h-4 w-4" />
            Preview
          </button>
        </div>

        <div className='flex gap-2 items-center'>
          <button onClick={handleExport} className='px-4 py-1 bg-blue-600 hover:bg-blue-500 text-white rounded-md flex items-center gap-2'>
            <CloudDownload className='h-4 w-4'/>
            Export
          </button>
          {sandboxInfo ? (
            <button
              onClick={() => handleDeploy(true)}
              className='px-4 py-1 bg-violet-600 hover:bg-violet-500 text-white rounded-md flex items-center gap-2'
              disabled={deploying}
            >
              {deploying ? 'Opening...' : 'Share'}
              <ExternalLink className='h-4 w-4' />
            </button>
          ) : (
            <button
              onClick={() => handleDeploy(false)}
              className='px-4 py-1 bg-green-600 hover:bg-green-500 text-white rounded-md flex items-center gap-2'
              disabled={deploying || !sandboxInfo}
            >
              {deploying ? 'Opening...' : 'Deploy'}
              <TrendingUp className='h-4 w-4' />
            </button>
          )}
        </div>
      </div>

      {activeTab === 'code' && renderCodeEditor()}
      {activeTab === 'preview' && renderPreview()}

      {loading && (
        <div className='p-10 bg-gray-950 opacity-80 gap-1 absolute rounded-lg w-full h-full flex items-center justify-center'>
          <Loader className={`animate-spin h-10 w-10 text-white `}/>
          <div className='text-center'>
            <h2 className='text-white font-semibold text-xl'>
              {editMode === 'incremental' ? 'Adding feature...' : 'Generating code...'}
            </h2>
            {editMode === 'incremental' && (
              <p className='text-gray-300 text-sm mt-2'>Making targeted changes to existing code</p>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default CodeView
